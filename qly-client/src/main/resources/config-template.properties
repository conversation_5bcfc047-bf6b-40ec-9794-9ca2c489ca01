# qly-client Configuration Template
# ==========================================
# This file is a configuration template, please modify according to your environment

# Reporter Kafka Basic Configuration
# ==========================================
# Enable/Disable Reporter functionality, default is true
# Options: true, false
qly.reporter.kafka.enabled=true

# Kafka server addresses, supports multiple addresses separated by commas
# Format: host1:port1,host2:port2,host3:port3
qly.reporter.kafka.bootstrap-servers=localhost:9092

# Report data topic name
# Recommended to distinguish by environment: qly_report_topic_dev, qly_report_topic_test, qly_report_topic_prod
qly.reporter.kafka.topic=qly_report_topic

# Kafka Producer Performance Configuration
# ==========================================
# Retry count for failed sends
# Recommended: dev 1-3, prod 3-5
qly.reporter.kafka.producer.retries=3

# Batch send size in bytes
# Recommended: dev 4096-8192, prod 16384-32768
qly.reporter.kafka.producer.batch-size=16384

# Linger time in milliseconds
# Recommended: dev 1-5, prod 5-10
qly.reporter.kafka.producer.linger-ms=1

# Buffer memory size in bytes
# Recommended: dev 8MB-16MB, prod 32MB-64MB
qly.reporter.kafka.producer.buffer-memory=33554432

# Acknowledgment mode, controls message reliability
# Options: 
#   0 - No acknowledgment, highest performance but may lose messages
#   1 - Wait for leader acknowledgment, balanced performance and reliability
#   all - Wait for all replicas acknowledgment, highest reliability but lower performance
qly.reporter.kafka.producer.acks=1

# Serializer configuration (usually no need to modify)
# ==========================================
qly.reporter.kafka.producer.key-serializer=org.apache.kafka.common.serialization.StringSerializer
qly.reporter.kafka.producer.value-serializer=org.apache.kafka.common.serialization.StringSerializer

# Spring Boot Application Configuration
# ==========================================
spring.application.name=qly-client

# Logging Configuration
# ==========================================
# qly-client component log level
# Options: TRACE, DEBUG, INFO, WARN, ERROR
logging.level.com.yy.hd.qly.client=DEBUG

# Kafka client log level
logging.level.org.apache.kafka=WARN

# Spring Kafka log level
logging.level.org.springframework.kafka=INFO

# Root log level
logging.level.root=INFO

# Other Optional Configuration
# ==========================================
# If you need custom Gson configuration, you can add the following
# gson.date-format=yyyy-MM-dd HH:mm:ss
# gson.serialize-nulls=true
# gson.pretty-printing=false

# Environment-specific examples:
# ==========================================
# Development Environment:
# qly.reporter.kafka.bootstrap-servers=dev-kafka:9092
# qly.reporter.kafka.producer.retries=1
# qly.reporter.kafka.producer.batch-size=8192
# logging.level.com.yy.hd.qly.client=DEBUG

# Production Environment:
# qly.reporter.kafka.bootstrap-servers=prod-kafka1:9092,prod-kafka2:9092,prod-kafka3:9092
# qly.reporter.kafka.producer.retries=5
# qly.reporter.kafka.producer.batch-size=32768
# qly.reporter.kafka.producer.acks=all
# logging.level.com.yy.hd.qly.client=INFO
