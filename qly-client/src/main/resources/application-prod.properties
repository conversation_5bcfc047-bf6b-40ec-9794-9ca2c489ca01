# Production Environment Configuration
# ==========================================

# Reporter Kafka Configuration - Production
# ==========================================
qly.reporter.kafka.enabled=true
qly.reporter.kafka.bootstrap-servers=prod-kafka-cluster-1:9092,prod-kafka-cluster-2:9092,prod-kafka-cluster-3:9092
qly.reporter.kafka.topic=qly_report_topic

# Kafka Producer Configuration - Production
# ==========================================
qly.reporter.kafka.producer.retries=5
qly.reporter.kafka.producer.batch-size=32768
qly.reporter.kafka.producer.linger-ms=10
qly.reporter.kafka.producer.buffer-memory=67108864
qly.reporter.kafka.producer.acks=all

# Logging Configuration - Production
# ==========================================
logging.level.com.yy.hd.qly.client=INFO
logging.level.org.apache.kafka=WARN
logging.level.org.springframework.kafka=WARN
logging.level.root=WARN
