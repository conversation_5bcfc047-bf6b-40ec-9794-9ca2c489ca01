# qly-client 配置
qly:
  reporter:
    kafka:
      # 是否启用Reporter
      enabled: true
      # Kafka服务器地址
      bootstrap-servers: localhost:9092
      # 上报数据的Topic名称
      topic: qly_report_topic
      # 生产者配置
      producer:
        # 重试次数
        retries: 3
        # 批量发送大小
        batch-size: 16384
        # 延迟发送时间(毫秒)
        linger-ms: 1
        # 缓冲区大小
        buffer-memory: 33554432
        # 确认模式: all, 1, 0
        acks: "1"
        # 序列化器
        key-serializer: org.apache.kafka.common.serialization.StringSerializer
        value-serializer: org.apache.kafka.common.serialization.StringSerializer

# Spring Boot 配置
spring:
  application:
    name: qly-client

# 日志配置
logging:
  level:
    com.yy.hd.qly.client: DEBUG
    org.apache.kafka: WARN
