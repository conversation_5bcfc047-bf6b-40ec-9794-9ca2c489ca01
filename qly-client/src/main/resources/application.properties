# qly-client Configuration File
# ==========================================

# Reporter Kafka Configuration
# ==========================================
# Enable/Disable Reporter
qly.reporter.kafka.enabled=true
# Kafka Server Address
#qly.reporter.kafka.bootstrap-servers=localhost:9092
# Report Data Topic Name
qly.reporter.kafka.topic=qly_report_topic

# Kafka Producer Configuration
# ==========================================
# Retry Count
qly.reporter.kafka.producer.retries=3
# Batch Send Size
qly.reporter.kafka.producer.batch-size=16384
# Linger Time (milliseconds)
qly.reporter.kafka.producer.linger-ms=1
# Buffer Memory Size
qly.reporter.kafka.producer.buffer-memory=33554432
# Acknowledgment Mode: all, 1, 0
qly.reporter.kafka.producer.acks=1
# Serializers
qly.reporter.kafka.producer.key-serializer=org.apache.kafka.common.serialization.StringSerializer
qly.reporter.kafka.producer.value-serializer=org.apache.kafka.common.serialization.StringSerializer

# Spring Boot Configuration
# ==========================================
spring.application.name=qly-client

# Logging Configuration
# ==========================================
logging.level.com.yy.hd.qly.client=DEBUG
logging.level.org.apache.kafka=WARN
logging.level.org.springframework.kafka=INFO
