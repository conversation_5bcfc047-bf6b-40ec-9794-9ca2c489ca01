# Test Environment Configuration
# ==========================================

# Reporter Kafka Configuration - Test
# ==========================================
qly.reporter.kafka.enabled=false
qly.reporter.kafka.bootstrap-servers=kafkafs0014-test001.yy.com:8102,kafkafs0014-test002.yy.com:8102,kafkafs0014-test003.yy.com:8102,kafkafs0014-test004.yy.com:8102,kafkafs0014-test005.yy.com:8102
qly.reporter.kafka.topic=qly_report_topic

# Kafka Producer Configuration - Test
# ==========================================
qly.reporter.kafka.producer.retries=1
qly.reporter.kafka.producer.batch-size=4096
qly.reporter.kafka.producer.linger-ms=1
qly.reporter.kafka.producer.buffer-memory=8388608
qly.reporter.kafka.producer.acks=1

# Logging Configuration - Test
# ==========================================
logging.level.com.yy.hd.qly.client=DEBUG
logging.level.org.apache.kafka=INFO
logging.level.org.springframework.kafka=INFO
logging.level.root=INFO
