package com.yy.hd.qly.client.util;

import lombok.extern.slf4j.Slf4j;
import org.springframework.core.env.Environment;

import java.io.IOException;
import java.io.InputStream;
import java.util.Properties;

/**
 * 配置工具类
 * 
 * <AUTHOR>
 * @date 2025-07-31 17:10
 */
@Slf4j
public class ConfigUtils {
    
    /**
     * 从classpath加载properties配置文件
     * 
     * @param fileName 配置文件名
     * @return Properties对象
     */
    public static Properties loadPropertiesFromClasspath(String fileName) {
        Properties properties = new Properties();
        try (InputStream inputStream = ConfigUtils.class.getClassLoader().getResourceAsStream(fileName)) {
            if (inputStream != null) {
                properties.load(inputStream);
                log.info("成功加载配置文件: {}", fileName);
            } else {
                log.warn("配置文件不存在: {}", fileName);
            }
        } catch (IOException e) {
            log.error("加载配置文件失败: {}", fileName, e);
        }
        return properties;
    }
    
    /**
     * 获取配置值，支持默认值
     * 
     * @param properties Properties对象
     * @param key 配置键
     * @param defaultValue 默认值
     * @return 配置值
     */
    public static String getProperty(Properties properties, String key, String defaultValue) {
        return properties.getProperty(key, defaultValue);
    }
    
    /**
     * 获取整型配置值
     * 
     * @param properties Properties对象
     * @param key 配置键
     * @param defaultValue 默认值
     * @return 配置值
     */
    public static int getIntProperty(Properties properties, String key, int defaultValue) {
        String value = properties.getProperty(key);
        if (value != null && !value.trim().isEmpty()) {
            try {
                return Integer.parseInt(value.trim());
            } catch (NumberFormatException e) {
                log.warn("配置值格式错误，使用默认值. key: {}, value: {}, default: {}", key, value, defaultValue);
            }
        }
        return defaultValue;
    }
    
    /**
     * 获取长整型配置值
     * 
     * @param properties Properties对象
     * @param key 配置键
     * @param defaultValue 默认值
     * @return 配置值
     */
    public static long getLongProperty(Properties properties, String key, long defaultValue) {
        String value = properties.getProperty(key);
        if (value != null && !value.trim().isEmpty()) {
            try {
                return Long.parseLong(value.trim());
            } catch (NumberFormatException e) {
                log.warn("配置值格式错误，使用默认值. key: {}, value: {}, default: {}", key, value, defaultValue);
            }
        }
        return defaultValue;
    }
    
    /**
     * 获取布尔型配置值
     * 
     * @param properties Properties对象
     * @param key 配置键
     * @param defaultValue 默认值
     * @return 配置值
     */
    public static boolean getBooleanProperty(Properties properties, String key, boolean defaultValue) {
        String value = properties.getProperty(key);
        if (value != null && !value.trim().isEmpty()) {
            return Boolean.parseBoolean(value.trim());
        }
        return defaultValue;
    }
    
    /**
     * 从Spring Environment获取配置值
     * 
     * @param environment Spring Environment
     * @param key 配置键
     * @param defaultValue 默认值
     * @return 配置值
     */
    public static String getProperty(Environment environment, String key, String defaultValue) {
        return environment.getProperty(key, defaultValue);
    }
    
    /**
     * 从Spring Environment获取整型配置值
     * 
     * @param environment Spring Environment
     * @param key 配置键
     * @param defaultValue 默认值
     * @return 配置值
     */
    public static int getIntProperty(Environment environment, String key, int defaultValue) {
        return environment.getProperty(key, Integer.class, defaultValue);
    }
    
    /**
     * 从Spring Environment获取长整型配置值
     * 
     * @param environment Spring Environment
     * @param key 配置键
     * @param defaultValue 默认值
     * @return 配置值
     */
    public static long getLongProperty(Environment environment, String key, long defaultValue) {
        return environment.getProperty(key, Long.class, defaultValue);
    }
    
    /**
     * 从Spring Environment获取布尔型配置值
     * 
     * @param environment Spring Environment
     * @param key 配置键
     * @param defaultValue 默认值
     * @return 配置值
     */
    public static boolean getBooleanProperty(Environment environment, String key, boolean defaultValue) {
        return environment.getProperty(key, Boolean.class, defaultValue);
    }
    
    /**
     * 打印所有qly相关的配置
     * 
     * @param environment Spring Environment
     */
    public static void printQlyConfig(Environment environment) {
        log.info("=== QlyClient 配置信息 ===");
        log.info("qly.reporter.kafka.enabled: {}", environment.getProperty("qly.reporter.kafka.enabled"));
        log.info("qly.reporter.kafka.bootstrap-servers: {}", environment.getProperty("qly.reporter.kafka.bootstrap-servers"));
        log.info("qly.reporter.kafka.topic: {}", environment.getProperty("qly.reporter.kafka.topic"));
        log.info("qly.reporter.kafka.producer.retries: {}", environment.getProperty("qly.reporter.kafka.producer.retries"));
        log.info("qly.reporter.kafka.producer.batch-size: {}", environment.getProperty("qly.reporter.kafka.producer.batch-size"));
        log.info("qly.reporter.kafka.producer.linger-ms: {}", environment.getProperty("qly.reporter.kafka.producer.linger-ms"));
        log.info("qly.reporter.kafka.producer.buffer-memory: {}", environment.getProperty("qly.reporter.kafka.producer.buffer-memory"));
        log.info("qly.reporter.kafka.producer.acks: {}", environment.getProperty("qly.reporter.kafka.producer.acks"));
        log.info("========================");
    }
}
