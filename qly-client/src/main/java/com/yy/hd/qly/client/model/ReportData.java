package com.yy.hd.qly.client.model;

import com.google.gson.annotations.SerializedName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * 数据上报模型
 *
 * <AUTHOR>
 * @date 2025-07-31 15:40
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class ReportData extends BaseReportData {


    /**
     * 活动id
     */
    @SerializedName("actId")
    private Long actId;

    /**
     * 组件id
     */
    @SerializedName("cmptId")
    private Long cmptId;

    /**
     * 组件索引
     */
    @SerializedName("cmptIndex")
    private Integer cmptIndex;

    /**
     * 关联的榜单id，如无，填入0
     */
    @SerializedName("rankId")
    private Long rankId;

    /**
     * 关联的阶段id，如无，填入0
     */
    @SerializedName("phaseId")
    private Long phaseId;

    /**
     * 关联的榜单时间key，如无，填入0
     */
    @SerializedName("timeCode")
    private Long timeCode;

    /**
     * 产生这条数据的uid,如无，填入0
     */
    @SerializedName("uid")
    private Long uid;

    /**
     * 产生这条数据的sid,如无，填入0
     */
    @SerializedName("sid")
    private Long sid;

    /**
     * 产生这条数据的ssid,如无，填入0
     */
    @SerializedName("ssid")
    private Long ssid;


}
