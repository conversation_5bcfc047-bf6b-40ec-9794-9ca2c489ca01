package com.yy.hd.qly.client.config;

import org.apache.kafka.clients.producer.ProducerConfig;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.kafka.core.DefaultKafkaProducerFactory;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.core.ProducerFactory;

import java.util.HashMap;
import java.util.Map;

/**
 * Kafka生产者配置
 * 
 * <AUTHOR>
 * @date 2025-07-31 15:40
 */
@Configuration
@ConditionalOnProperty(prefix = "qly.reporter.kafka", name = "enabled", havingValue = "true", matchIfMissing = true)
public class KafkaProducerConfig {
    
    @Autowired
    private ReporterKafkaConfig reporterKafkaConfig;
    
    @Bean("reporterProducerFactory")
    public ProducerFactory<String, String> reporterProducerFactory() {
        Map<String, Object> configProps = new HashMap<>();
        configProps.put(ProducerConfig.BOOTSTRAP_SERVERS_CONFIG, reporterKafkaConfig.getBootstrapServers());
        configProps.put(ProducerConfig.KEY_SERIALIZER_CLASS_CONFIG, reporterKafkaConfig.getProducer().getKeySerializer());
        configProps.put(ProducerConfig.VALUE_SERIALIZER_CLASS_CONFIG, reporterKafkaConfig.getProducer().getValueSerializer());
        configProps.put(ProducerConfig.RETRIES_CONFIG, reporterKafkaConfig.getProducer().getRetries());
        configProps.put(ProducerConfig.BATCH_SIZE_CONFIG, reporterKafkaConfig.getProducer().getBatchSize());
        configProps.put(ProducerConfig.LINGER_MS_CONFIG, reporterKafkaConfig.getProducer().getLingerMs());
        configProps.put(ProducerConfig.BUFFER_MEMORY_CONFIG, reporterKafkaConfig.getProducer().getBufferMemory());
        configProps.put(ProducerConfig.ACKS_CONFIG, reporterKafkaConfig.getProducer().getAcks());
        
        return new DefaultKafkaProducerFactory<>(configProps);
    }
    
    @Bean("reporterKafkaTemplate")
    public KafkaTemplate<String, String> reporterKafkaTemplate() {
        return new KafkaTemplate<>(reporterProducerFactory());
    }
}
