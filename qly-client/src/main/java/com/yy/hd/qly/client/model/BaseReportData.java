package com.yy.hd.qly.client.model;

import com.google.gson.annotations.SerializedName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * desc:
 *
 * <AUTHOR>
 * @date 2025-07-31 18:31
 **/
@Data
//@Builder
//@NoArgsConstructor
//@AllArgsConstructor
public class BaseReportData {
    /**
     * 批次traceId，使用场景举例：串联活动全程结算事件。开启自动累榜时设置，设置好后，全局使用同1个batchTraceId
     */
    @SerializedName("batchTraceId")
    protected String batchTraceId;

    /**
     * otel的traceId，用于串联调用链路
     */
    @SerializedName("traceId")
    protected String traceId;

    /**
     * 业务时间(毫秒)
     */
    @SerializedName("traceBeginTime")
    protected Long traceBeginTime;

    /**
     * 产品，如hdpt、zhuiwan
     */
    @SerializedName("product")
    protected String product;

    /**
     * 服务进程，如stream、xlogic、ranking
     */
    @SerializedName("service")
    protected String service;

    /**
     * 场景，可以是事件名称、源头入口方法名称等，例如 榜单结算事件、单次送礼累榜updateRanking
     */
    @SerializedName("scene")
    protected String scene;

    /**
     * 上报埋点方法，例如 commonNoticeUnicast 单播
     */
    @SerializedName("method")
    protected String method;

    /**
     * 数据类型: 1==切面自动上报 2==人工打点上报
     */
    @SerializedName("dataType")
    protected Integer dataType;

    /**
     * 预留查询条件
     */
    @SerializedName("key1")
    protected String key1;

    /**
     * 预留查询条件
     */
    @SerializedName("key2")
    protected String key2;

    /**
     * 预留查询条件
     */
    @SerializedName("key3")
    protected String key3;

    /**
     * 上报主要内容，如 累榜黑名单校验不通过，不累榜
     */
    @SerializedName("content")
    protected String content;

    /**
     * 预留扩展字段
     */
    @SerializedName("extData")
    protected String extData;

    /**
     * 上报时间戳(毫秒)
     */
    @SerializedName("timestamp")
    protected Long timestamp;
}
