package com.yy.hd.qly.client;

import com.google.gson.Gson;
import com.yy.hd.qly.client.config.ReporterKafkaConfig;
import com.yy.hd.qly.client.model.ActReportData;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.support.SendResult;
import org.springframework.stereotype.Component;
import org.springframework.util.concurrent.ListenableFuture;
import org.springframework.util.concurrent.ListenableFutureCallback;

import java.util.concurrent.CompletableFuture;

/**
 * 数据上报客户端
 * 用于向Kafka发送数据上报消息
 *
 * <AUTHOR>
 * @date 2025-07-31 15:40
 **/
@Slf4j
@Component
@ConditionalOnProperty(prefix = "qly.reporter.kafka", name = "enabled", havingValue = "true", matchIfMissing = true)
public class ReporterClient {

    @Autowired
    @Qualifier("reporterKafkaTemplate")
    private KafkaTemplate<String, String> kafkaTemplate;

    @Autowired
    private ReporterKafkaConfig kafkaConfig;

    @Autowired
    @Qualifier("reporterGson")
    private Gson gson;

    /**
     * 同步发送数据上报
     *
     * @param actReportData 上报数据
     * @return 是否发送成功
     */
    public boolean sendSync(ActReportData actReportData) {
        try {
            // 设置默认时间戳
            if (actReportData.getTimestamp() == null) {
                actReportData.setTimestamp(System.currentTimeMillis());
            }

            String jsonData = gson.toJson(actReportData);
            String key = generateKey(actReportData);

            SendResult<String, String> result = kafkaTemplate.send(kafkaConfig.getTopic(), key, jsonData).get();
            log.debug("数据上报成功发送到Kafka, topic: {}, key: {}, offset: {}",
                     kafkaConfig.getTopic(), key, result.getRecordMetadata().offset());
            return true;
        } catch (Exception e) {
            log.error("数据上报发送失败", e);
            return false;
        }
    }

    /**
     * 异步发送数据上报
     *
     * @param actReportData 上报数据
     * @return CompletableFuture<Boolean> 异步结果
     */
    public CompletableFuture<Boolean> sendAsync(ActReportData actReportData) {
        CompletableFuture<Boolean> future = new CompletableFuture<>();

        try {
            // 设置默认时间戳
            if (actReportData.getTimestamp() == null) {
                actReportData.setTimestamp(System.currentTimeMillis());
            }

            String jsonData = gson.toJson(actReportData);
            String key = generateKey(actReportData);

            ListenableFuture<SendResult<String, String>> kafkaFuture =
                kafkaTemplate.send(kafkaConfig.getTopic(), key, jsonData);

            kafkaFuture.addCallback(new ListenableFutureCallback<SendResult<String, String>>() {
                @Override
                public void onSuccess(SendResult<String, String> result) {
                    log.debug("数据上报异步发送成功, topic: {}, key: {}, offset: {}",
                             kafkaConfig.getTopic(), key, result.getRecordMetadata().offset());
                    future.complete(true);
                }

                @Override
                public void onFailure(Throwable ex) {
                    log.error("数据上报异步发送失败, key: {}", key, ex);
                    future.complete(false);
                }
            });

        } catch (Exception e) {
            log.error("数据上报JSON序列化失败", e);
            future.complete(false);
        }

        return future;
    }

    /**
     * 发送数据上报（默认异步）
     *
     * @param actReportData 上报数据
     */
    public void send(ActReportData actReportData) {
        sendAsync(actReportData);
    }

    /**
     * 生成Kafka消息的Key
     * 使用traceId作为key，如果没有则使用batchTraceId，都没有则使用时间戳
     *
     * @param actReportData 上报数据
     * @return 消息key
     */
    private String generateKey(ActReportData actReportData) {
        if (actReportData.getTraceId() != null && !actReportData.getTraceId().isEmpty()) {
            return actReportData.getTraceId();
        }
        if (actReportData.getBatchTraceId() != null && !actReportData.getBatchTraceId().isEmpty()) {
            return actReportData.getBatchTraceId();
        }
        return String.valueOf(actReportData.getTimestamp());
    }

    /**
     * 创建ReportData构建器，并设置一些默认值
     *
     * @return ReportData.ReportDataBuilder
     */
    public static ActReportData.ReportDataBuilder builder() {
        return ActReportData.builder()
                .timestamp(System.currentTimeMillis())
                .dataType(2) // 默认为人工打点上报
                .actId(0L)
                .cmptId(0L)
                .cmptIndex(0)
                .rankId(0L)
                .phaseId(0L)
                .timeCode(0L)
                .uid(0L)
                .sid(0L)
                .ssid(0L);
    }

    /**
     * 快速创建切面自动上报数据
     *
     * @param product 产品
     * @param service 服务
     * @param scene 场景
     * @param method 方法
     * @param content 内容
     * @return ReportData
     */
    public static ActReportData createAutoReport(String product, String service, String scene,
                                                 String method, String content) {
        return builder()
                .product(product)
                .service(service)
                .scene(scene)
                .method(method)
                .content(content)
                .dataType(1) // 切面自动上报
                .build();
    }

    /**
     * 快速创建人工打点上报数据
     *
     * @param product 产品
     * @param service 服务
     * @param scene 场景
     * @param method 方法
     * @param content 内容
     * @return ReportData
     */
    public static ActReportData createManualReport(String product, String service, String scene,
                                                   String method, String content) {
        return builder()
                .product(product)
                .service(service)
                .scene(scene)
                .method(method)
                .content(content)
                .dataType(2) // 人工打点上报
                .build();
    }
}
