package com.yy.hd.qly.client.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * Reporter Kafka配置
 * 
 * <AUTHOR>
 * @date 2025-07-31 15:40
 */
@Data
@ConfigurationProperties(prefix = "qly.reporter.kafka")
public class ReporterKafkaConfig {

    /**
     * Kafka服务器地址
     */
    private String bootstrapServers = "localhost:9092";

    /**
     * 上报数据的Topic名称
     */
    private String topic = "qly_report_topic";

    /**
     * 生产者配置
     */
    private Producer producer = new Producer();

    /**
     * 是否启用Reporter
     */
    private boolean enabled = true;

    @Data
    public static class Producer {
        /**
         * 重试次数
         */
        private int retries = 3;

        /**
         * 批量发送大小
         */
        private int batchSize = 16384;

        /**
         * 延迟发送时间(毫秒)
         */
        private int lingerMs = 1;

        /**
         * 缓冲区大小
         */
        private long bufferMemory = 33554432L;

        /**
         * 确认模式: all, 1, 0
         */
        private String acks = "1";

        /**
         * 序列化器
         */
        private String keySerializer = "org.apache.kafka.common.serialization.StringSerializer";
        private String valueSerializer = "org.apache.kafka.common.serialization.StringSerializer";
    }
}
