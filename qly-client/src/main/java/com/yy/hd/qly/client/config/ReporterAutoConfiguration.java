package com.yy.hd.qly.client.config;

import com.yy.hd.qly.client.ReporterClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.SpringBootConfiguration;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;

import javax.annotation.PostConstruct;

/**
 * Reporter自动配置类
 * 
 * <AUTHOR>
 * @date 2025-07-31 15:40
 */
@Slf4j
@SpringBootConfiguration
@Configuration
@EnableConfigurationProperties(ReporterKafkaConfig.class)
@ComponentScan(basePackageClasses = ReporterClient.class)
@ConditionalOnProperty(prefix = "qly.reporter.kafka", name = "enabled", havingValue = "true", matchIfMissing = true)
public class ReporterAutoConfiguration {

    @PostConstruct
    public void init() {
        log.info("QlyReporter 自动配置已启用");
    }
}
