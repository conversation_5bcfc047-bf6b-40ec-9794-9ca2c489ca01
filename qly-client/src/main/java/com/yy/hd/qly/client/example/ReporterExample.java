package com.yy.hd.qly.client.example;

import com.yy.hd.qly.client.ReporterClient;
import com.yy.hd.qly.client.model.ReportData;
import com.yy.hd.qly.client.util.ConfigUtils;
import com.yy.hd.qly.client.util.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;

import java.util.concurrent.CompletableFuture;

/**
 * ReporterClient使用示例
 * 
 * <AUTHOR>
 * @date 2025-07-31 15:40
 */
@Slf4j
@Component
public class ReporterExample {

    @Autowired
    private ReporterClient reporterClient;

    @Autowired
    private Environment environment;
    
    /**
     * 示例1：基本使用方式
     */
    public void basicUsage() {
        // 创建上报数据
        ReportData reportData = ReportData.builder()
                .batchTraceId("batch_001")
                .traceId("trace_001")
                .traceBeginTime(System.currentTimeMillis())
                .product("hdpt")
                .service("ranking")
                .scene("榜单结算事件")
                .method("updateRanking")
                .dataType(1) // 切面自动上报
                .actId(12345L)
                .cmptId(67890L)
                .cmptIndex(1)
                .rankId(111L)
                .phaseId(222L)
                .timeCode(333L)
                .uid(1001L)
                .sid(2001L)
                .ssid(3001L)
                .key1("key1_value")
                .key2("key2_value")
                .key3("key3_value")
                .content("榜单结算成功")
                .extData("{\"extra\":\"data\"}")
                .build();
        
        // 异步发送（推荐）
        reporterClient.send(reportData);
        
        // 或者同步发送
        boolean success = reporterClient.sendSync(reportData);
        log.info("同步发送结果: {}", success);
    }
    
    /**
     * 示例2：使用快速创建方法
     */
    public void quickUsage() {
        // 创建切面自动上报数据
        ReportData autoReport = ReporterClient.createAutoReport(
                "hdpt", 
                "stream", 
                "单次送礼累榜", 
                "commonNoticeUnicast", 
                "累榜成功"
        );
        
        // 可以继续设置其他字段
        autoReport.setActId(12345L);
        autoReport.setUid(1001L);
        autoReport.setTraceId("trace_002");
        
        // 发送
        reporterClient.send(autoReport);
        
        // 创建人工打点上报数据
        ReportData manualReport = ReporterClient.createManualReport(
                "zhuiwan", 
                "xlogic", 
                "黑名单校验", 
                "checkBlacklist", 
                "累榜黑名单校验不通过，不累榜"
        );
        
        manualReport.setActId(54321L);
        manualReport.setUid(2001L);
        
        // 异步发送并处理结果
        CompletableFuture<Boolean> future = reporterClient.sendAsync(manualReport);
        future.thenAccept(result -> {
            if (result) {
                log.info("异步发送成功");
            } else {
                log.error("异步发送失败");
            }
        });
    }
    
    /**
     * 示例3：批量上报场景
     */
    public void batchReporting() {
        String batchTraceId = "batch_" + System.currentTimeMillis();
        
        // 模拟批量处理场景，使用相同的batchTraceId
        for (int i = 0; i < 5; i++) {
            ReportData reportData = ReporterClient.builder()
                    .batchTraceId(batchTraceId)
                    .traceId("trace_" + i)
                    .product("hdpt")
                    .service("ranking")
                    .scene("批量结算")
                    .method("batchSettle")
                    .content("批量结算第" + (i + 1) + "条记录")
                    .actId(12345L)
                    .uid(1000L + i)
                    .build();
            
            reporterClient.send(reportData);
        }
    }

    /**
     * 示例4：JSON工具类使用
     */
    public void jsonUtilsUsage() {
        // 创建测试数据
        ReportData reportData = ReporterClient.createManualReport(
                "hdpt", "test", "JSON工具测试", "jsonTest", "测试JSON序列化"
        );
        reportData.setActId(99999L);
        reportData.setUid(8888L);

        // 转换为JSON字符串
        String json = JsonUtils.toJson(reportData);
        log.info("JSON字符串: {}", json);

        // 转换为格式化的JSON字符串
        String prettyJson = JsonUtils.toPrettyJson(reportData);
        log.info("格式化JSON字符串: \n{}", prettyJson);

        // 从JSON字符串转换回对象
        ReportData parsedData = JsonUtils.fromJson(json, ReportData.class);
        log.info("解析后的对象: {}", parsedData);

        // 深拷贝对象
        ReportData copiedData = JsonUtils.deepCopy(reportData, ReportData.class);
        log.info("深拷贝对象: {}", copiedData);

        // 验证JSON有效性
        boolean isValid = JsonUtils.isValidJson(json);
        log.info("JSON有效性: {}", isValid);

        // 发送原始数据
        reporterClient.send(reportData);
    }

    /**
     * 示例5：配置工具类使用
     */
    public void configUtilsUsage() {
        // 打印当前配置信息
        ConfigUtils.printQlyConfig(environment);

        // 获取配置值
        boolean enabled = ConfigUtils.getBooleanProperty(environment, "qly.reporter.kafka.enabled", true);
        String bootstrapServers = ConfigUtils.getProperty(environment, "qly.reporter.kafka.bootstrap-servers", "localhost:9092");
        String topic = ConfigUtils.getProperty(environment, "qly.reporter.kafka.topic", "qly_report_topic");
        int retries = ConfigUtils.getIntProperty(environment, "qly.reporter.kafka.producer.retries", 3);

        log.info("当前配置 - enabled: {}, servers: {}, topic: {}, retries: {}",
                enabled, bootstrapServers, topic, retries);

        // 根据配置创建测试数据
        if (enabled) {
            ReportData reportData = ReporterClient.createManualReport(
                    "hdpt", "config", "配置测试", "configTest",
                    String.format("使用配置: topic=%s, retries=%d", topic, retries)
            );
            reportData.setActId(88888L);

            reporterClient.send(reportData);
            log.info("已发送配置测试数据到topic: {}", topic);
        } else {
            log.info("Reporter已禁用，跳过发送");
        }
    }
}
