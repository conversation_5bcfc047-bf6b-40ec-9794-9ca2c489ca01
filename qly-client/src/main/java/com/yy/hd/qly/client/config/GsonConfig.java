package com.yy.hd.qly.client.config;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * Gson配置类
 * 
 * <AUTHOR>
 * @date 2025-07-31 17:00
 */
@Configuration
@ConditionalOnProperty(prefix = "qly.reporter.kafka", name = "enabled", havingValue = "true", matchIfMissing = true)
public class GsonConfig {
    
    /**
     * 配置Gson实例
     * 
     * @return Gson实例
     */
    @Bean("reporterGson")
    public Gson reporterGson() {
        return new GsonBuilder()
                .setDateFormat("yyyy-MM-dd HH:mm:ss") // 设置日期格式
                .disableHtmlEscaping() // 禁用HTML转义
                .serializeNulls() // 序列化null值
                .setPrettyPrinting() // 格式化输出（可选，用于调试）
                .create();
    }
}
