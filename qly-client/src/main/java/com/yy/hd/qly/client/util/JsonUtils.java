package com.yy.hd.qly.client.util;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.JsonSyntaxException;
import lombok.extern.slf4j.Slf4j;

import java.lang.reflect.Type;

/**
 * JSON工具类，基于Gson实现
 * 
 * <AUTHOR>
 * @date 2025-07-31 17:00
 */
@Slf4j
public class JsonUtils {
    
    private static final Gson GSON = new GsonBuilder()
            .setDateFormat("yyyy-MM-dd HH:mm:ss")
            .disableHtmlEscaping()
            .serializeNulls()
            .create();
    
    private static final Gson PRETTY_GSON = new GsonBuilder()
            .setDateFormat("yyyy-MM-dd HH:mm:ss")
            .disableHtmlEscaping()
            .serializeNulls()
            .setPrettyPrinting()
            .create();
    
    /**
     * 对象转JSON字符串
     * 
     * @param obj 对象
     * @return JSON字符串
     */
    public static String toJson(Object obj) {
        if (obj == null) {
            return null;
        }
        try {
            return GSON.toJson(obj);
        } catch (Exception e) {
            log.error("对象转JSON失败", e);
            return null;
        }
    }
    
    /**
     * 对象转格式化的JSON字符串
     * 
     * @param obj 对象
     * @return 格式化的JSON字符串
     */
    public static String toPrettyJson(Object obj) {
        if (obj == null) {
            return null;
        }
        try {
            return PRETTY_GSON.toJson(obj);
        } catch (Exception e) {
            log.error("对象转格式化JSON失败", e);
            return null;
        }
    }
    
    /**
     * JSON字符串转对象
     * 
     * @param json JSON字符串
     * @param clazz 目标类型
     * @param <T> 泛型类型
     * @return 对象
     */
    public static <T> T fromJson(String json, Class<T> clazz) {
        if (json == null || json.trim().isEmpty()) {
            return null;
        }
        try {
            return GSON.fromJson(json, clazz);
        } catch (JsonSyntaxException e) {
            log.error("JSON转对象失败, json: {}, class: {}", json, clazz.getName(), e);
            return null;
        }
    }
    
    /**
     * JSON字符串转对象（支持泛型）
     * 
     * @param json JSON字符串
     * @param type 目标类型
     * @param <T> 泛型类型
     * @return 对象
     */
    public static <T> T fromJson(String json, Type type) {
        if (json == null || json.trim().isEmpty()) {
            return null;
        }
        try {
            return GSON.fromJson(json, type);
        } catch (JsonSyntaxException e) {
            log.error("JSON转对象失败, json: {}, type: {}", json, type.getTypeName(), e);
            return null;
        }
    }
    
    /**
     * 检查字符串是否为有效的JSON
     * 
     * @param json JSON字符串
     * @return 是否有效
     */
    public static boolean isValidJson(String json) {
        if (json == null || json.trim().isEmpty()) {
            return false;
        }
        try {
            GSON.fromJson(json, Object.class);
            return true;
        } catch (JsonSyntaxException e) {
            return false;
        }
    }
    
    /**
     * 深拷贝对象（通过JSON序列化/反序列化）
     * 
     * @param obj 源对象
     * @param clazz 目标类型
     * @param <T> 泛型类型
     * @return 拷贝后的对象
     */
    public static <T> T deepCopy(Object obj, Class<T> clazz) {
        if (obj == null) {
            return null;
        }
        try {
            String json = GSON.toJson(obj);
            return GSON.fromJson(json, clazz);
        } catch (Exception e) {
            log.error("深拷贝对象失败", e);
            return null;
        }
    }
}
