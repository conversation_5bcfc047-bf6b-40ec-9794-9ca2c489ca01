package com.yy.hd.qly.client.util;

import com.yy.hd.qly.client.ReporterClient;
import com.yy.hd.qly.client.model.ReportData;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

/**
 * JsonUtils测试类
 * 
 * <AUTHOR>
 * @date 2025-07-31 17:00
 */
class JsonUtilsTest {
    
    @Test
    void testToJson() {
        ReportData reportData = ReporterClient.createManualReport(
                "hdpt", "test", "JSON测试", "toJsonTest", "测试JSON序列化"
        );
        reportData.setActId(12345L);
        reportData.setUid(1001L);
        
        String json = JsonUtils.toJson(reportData);
        
        assertNotNull(json);
        assertTrue(json.contains("\"product\":\"hdpt\""));
        assertTrue(json.contains("\"service\":\"test\""));
        assertTrue(json.contains("\"scene\":\"JSON测试\""));
        assertTrue(json.contains("\"method\":\"toJsonTest\""));
        assertTrue(json.contains("\"content\":\"测试JSON序列化\""));
        assertTrue(json.contains("\"actId\":12345"));
        assertTrue(json.contains("\"uid\":1001"));
        assertTrue(json.contains("\"dataType\":2"));
    }
    
    @Test
    void testToPrettyJson() {
        ReportData reportData = ReporterClient.createAutoReport(
                "zhuiwan", "stream", "格式化测试", "prettyTest", "测试格式化JSON"
        );
        
        String prettyJson = JsonUtils.toPrettyJson(reportData);
        
        assertNotNull(prettyJson);
        assertTrue(prettyJson.contains("\n")); // 包含换行符，说明是格式化的
        assertTrue(prettyJson.contains("\"product\": \"zhuiwan\""));
        assertTrue(prettyJson.contains("\"dataType\": 1"));
    }
    
    @Test
    void testFromJson() {
        String json = "{\"product\":\"hdpt\",\"service\":\"ranking\",\"scene\":\"测试场景\",\"method\":\"testMethod\",\"content\":\"测试内容\",\"dataType\":2,\"actId\":99999,\"uid\":8888}";
        
        ReportData reportData = JsonUtils.fromJson(json, ReportData.class);
        
        assertNotNull(reportData);
        assertEquals("hdpt", reportData.getProduct());
        assertEquals("ranking", reportData.getService());
        assertEquals("测试场景", reportData.getScene());
        assertEquals("testMethod", reportData.getMethod());
        assertEquals("测试内容", reportData.getContent());
        assertEquals(Integer.valueOf(2), reportData.getDataType());
        assertEquals(Long.valueOf(99999L), reportData.getActId());
        assertEquals(Long.valueOf(8888L), reportData.getUid());
    }
    
    @Test
    void testDeepCopy() {
        ReportData original = ReporterClient.createManualReport(
                "hdpt", "test", "深拷贝测试", "deepCopyTest", "测试深拷贝"
        );
        original.setActId(54321L);
        original.setUid(9999L);
        
        ReportData copy = JsonUtils.deepCopy(original, ReportData.class);
        
        assertNotNull(copy);
        assertNotSame(original, copy); // 不是同一个对象
        assertEquals(original.getProduct(), copy.getProduct());
        assertEquals(original.getService(), copy.getService());
        assertEquals(original.getScene(), copy.getScene());
        assertEquals(original.getMethod(), copy.getMethod());
        assertEquals(original.getContent(), copy.getContent());
        assertEquals(original.getDataType(), copy.getDataType());
        assertEquals(original.getActId(), copy.getActId());
        assertEquals(original.getUid(), copy.getUid());
    }
    
    @Test
    void testIsValidJson() {
        // 有效的JSON
        String validJson = "{\"name\":\"test\",\"value\":123}";
        assertTrue(JsonUtils.isValidJson(validJson));
        
        // 无效的JSON
        String invalidJson = "{name:test,value:123,}"; // 末尾多了逗号
        assertFalse(JsonUtils.isValidJson(invalidJson));
        
        // 空字符串
        assertFalse(JsonUtils.isValidJson(""));
        assertFalse(JsonUtils.isValidJson(null));
        
        // 数组JSON
        String arrayJson = "[{\"name\":\"test1\"},{\"name\":\"test2\"}]";
        assertTrue(JsonUtils.isValidJson(arrayJson));
    }
    
    @Test
    void testNullHandling() {
        // 测试null对象
        assertNull(JsonUtils.toJson(null));
        assertNull(JsonUtils.toPrettyJson(null));
        assertNull(JsonUtils.fromJson(null, ReportData.class));
        assertNull(JsonUtils.fromJson("", ReportData.class));
        assertNull(JsonUtils.deepCopy(null, ReportData.class));
    }
}
