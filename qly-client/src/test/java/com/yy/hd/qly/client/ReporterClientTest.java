package com.yy.hd.qly.client;

import com.yy.hd.qly.client.model.ActReportData;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

/**
 * ReporterClient测试类
 *
 * <AUTHOR>
 * @date 2025-07-31 15:40
 */
class ReporterClientTest {
    
    @Test
    void testReportDataBuilder() {
        // 测试ReportData构建
        ActReportData actReportData = ReporterClient.builder()
                .product("hdpt")
                .service("ranking")
                .scene("测试场景")
                .method("testMethod")
                .content("测试内容")
                .build();
        
        assertNotNull(actReportData);
        assertEquals("hdpt", actReportData.getProduct());
        assertEquals("ranking", actReportData.getService());
        assertEquals("测试场景", actReportData.getScene());
        assertEquals("testMethod", actReportData.getMethod());
        assertEquals("测试内容", actReportData.getContent());
        assertEquals(Integer.valueOf(2), actReportData.getDataType()); // 默认人工打点
        assertNotNull(actReportData.getTimestamp());
    }
    
    @Test
    void testCreateAutoReport() {
        ActReportData autoReport = ReporterClient.createAutoReport(
                "hdpt", "stream", "自动上报测试", "autoMethod", "自动上报内容"
        );
        
        assertNotNull(autoReport);
        assertEquals("hdpt", autoReport.getProduct());
        assertEquals("stream", autoReport.getService());
        assertEquals("自动上报测试", autoReport.getScene());
        assertEquals("autoMethod", autoReport.getMethod());
        assertEquals("自动上报内容", autoReport.getContent());
        assertEquals(Integer.valueOf(1), autoReport.getDataType()); // 切面自动上报
    }
    
    @Test
    void testCreateManualReport() {
        ActReportData manualReport = ReporterClient.createManualReport(
                "zhuiwan", "xlogic", "手动上报测试", "manualMethod", "手动上报内容"
        );
        
        assertNotNull(manualReport);
        assertEquals("zhuiwan", manualReport.getProduct());
        assertEquals("xlogic", manualReport.getService());
        assertEquals("手动上报测试", manualReport.getScene());
        assertEquals("manualMethod", manualReport.getMethod());
        assertEquals("手动上报内容", manualReport.getContent());
        assertEquals(Integer.valueOf(2), manualReport.getDataType()); // 人工打点上报
    }

    @Test
    void testSendAsyncReturnType() {
        // 测试sendAsync方法的返回类型和基本行为
        ActReportData actReportData = ReporterClient.createManualReport(
                "hdpt", "test", "返回类型测试", "returnTypeTest", "测试返回类型"
        );

        // 由于没有Spring上下文，这里只能测试方法是否存在和返回类型
        // 实际的Kafka交互需要集成测试
        assertNotNull(actReportData);
        assertEquals("hdpt", actReportData.getProduct());
        assertEquals("test", actReportData.getService());
        assertEquals("返回类型测试", actReportData.getScene());
        assertEquals("returnTypeTest", actReportData.getMethod());
        assertEquals("测试返回类型", actReportData.getContent());
        assertEquals(Integer.valueOf(2), actReportData.getDataType());
    }

    @Test
    void testTimestampAutoSetting() {
        // 测试时间戳自动设置
        ActReportData actReportData = ActReportData.builder()
                .product("hdpt")
                .service("test")
                .scene("时间戳测试")
                .method("timestampTest")
                .content("测试自动设置时间戳")
                .dataType(2)
                .build();

        // 初始时间戳为null
        assertNull(actReportData.getTimestamp());

        // 手动设置时间戳
        long currentTime = System.currentTimeMillis();
        actReportData.setTimestamp(currentTime);
        assertEquals(currentTime, actReportData.getTimestamp());
    }

    @Test
    void testKeyGenerationLogic() {
        // 测试Key生成逻辑（通过ReportData字段验证）

        // 1. 有traceId的情况
        ActReportData actReportDataWithTraceId = ReporterClient.createManualReport(
                "hdpt", "test", "Key生成测试1", "keyGenTest1", "测试traceId作为key"
        );
        actReportDataWithTraceId.setTraceId("trace_123");
        actReportDataWithTraceId.setBatchTraceId("batch_456");

        assertNotNull(actReportDataWithTraceId.getTraceId());
        assertEquals("trace_123", actReportDataWithTraceId.getTraceId());

        // 2. 只有batchTraceId的情况
        ActReportData actReportDataWithBatchTraceId = ReporterClient.createManualReport(
                "hdpt", "test", "Key生成测试2", "keyGenTest2", "测试batchTraceId作为key"
        );
        actReportDataWithBatchTraceId.setBatchTraceId("batch_789");

        assertNull(actReportDataWithBatchTraceId.getTraceId());
        assertEquals("batch_789", actReportDataWithBatchTraceId.getBatchTraceId());

        // 3. 只有timestamp的情况
        ActReportData actReportDataWithTimestamp = ReporterClient.createManualReport(
                "hdpt", "test", "Key生成测试3", "keyGenTest3", "测试timestamp作为key"
        );
        actReportDataWithTimestamp.setTimestamp(1234567890L);

        assertNull(actReportDataWithTimestamp.getTraceId());
        assertNull(actReportDataWithTimestamp.getBatchTraceId());
        assertEquals(Long.valueOf(1234567890L), actReportDataWithTimestamp.getTimestamp());
    }

    @Test
    void testEmptyStringHandling() {
        // 测试空字符串的处理
        ActReportData actReportData = ReporterClient.createManualReport(
                "hdpt", "test", "空字符串测试", "emptyStringTest", "测试空字符串处理"
        );
        actReportData.setTraceId(""); // 空字符串
        actReportData.setBatchTraceId(""); // 空字符串
        actReportData.setTimestamp(9876543210L);

        assertEquals("", actReportData.getTraceId());
        assertEquals("", actReportData.getBatchTraceId());
        assertEquals(Long.valueOf(9876543210L), actReportData.getTimestamp());
    }

    @Test
    void testDataTypeSettings() {
        // 测试数据类型设置

        // 自动上报
        ActReportData autoReport = ReporterClient.createAutoReport(
                "hdpt", "stream", "自动上报", "autoMethod", "自动上报内容"
        );
        assertEquals(Integer.valueOf(1), autoReport.getDataType());

        // 手动上报
        ActReportData manualReport = ReporterClient.createManualReport(
                "zhuiwan", "xlogic", "手动上报", "manualMethod", "手动上报内容"
        );
        assertEquals(Integer.valueOf(2), manualReport.getDataType());

        // 使用builder默认值
        ActReportData builderReport = ReporterClient.builder()
                .product("test")
                .service("test")
                .scene("test")
                .method("test")
                .content("test")
                .build();
        assertEquals(Integer.valueOf(2), builderReport.getDataType()); // 默认为手动上报
    }
}
