package com.yy.hd.qly.client;

import com.yy.hd.qly.client.config.ReporterAutoConfiguration;
import com.yy.hd.qly.client.model.ReportData;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.condition.EnabledIfSystemProperty;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;

import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;

import static org.junit.jupiter.api.Assertions.*;

/**
 * ReporterClient集成测试类
 * 需要真实的Kafka环境才能运行
 * 
 * <AUTHOR>
 * @date 2025-07-31 18:00
 */
@SpringBootTest(classes = ReporterAutoConfiguration.class)
@TestPropertySource(value ={"classpath:application-test.properties","classpath:application.properties"})
@EnabledIfSystemProperty(named = "integration.test", matches = "true")
class ReporterClientIntegrationTest {

    @Autowired(required = false)
    private ReporterClient reporterClient;

    @Test
    void testSendAsyncIntegration() throws ExecutionException, InterruptedException, TimeoutException {
        // 如果没有Kafka环境，跳过测试
        if (reporterClient == null) {
            return;
        }

        // 准备测试数据
        ReportData reportData = ReporterClient.createManualReport(
                "hdpt", "test", "集成测试", "integrationTest", "测试sendAsync集成"
        );
        reportData.setActId(12345L);
        reportData.setUid(1001L);
        reportData.setTraceId("integration_trace_001");

        // 执行异步发送
        CompletableFuture<Boolean> future = reporterClient.sendAsync(reportData);

        // 验证结果
        assertNotNull(future);
        Boolean result = future.get(5, TimeUnit.SECONDS);
        assertTrue(result, "异步发送应该成功");

        // 验证时间戳被设置
        assertNotNull(reportData.getTimestamp());
        assertTrue(reportData.getTimestamp() > 0);
    }

    @Test
    void testSendAsyncWithNullTimestamp() throws ExecutionException, InterruptedException, TimeoutException {
        if (reporterClient == null) {
            return;
        }

        // 准备测试数据（不设置timestamp）
        ReportData reportData = ReportData.builder()
                .product("hdpt")
                .service("test")
                .scene("时间戳集成测试")
                .method("timestampIntegrationTest")
                .content("测试自动设置时间戳")
                .dataType(2)
                .actId(99999L)
                .uid(8888L)
                .build();

        // 确保timestamp为null
        reportData.setTimestamp(null);

        // 执行异步发送
        CompletableFuture<Boolean> future = reporterClient.sendAsync(reportData);

        // 验证结果
        assertNotNull(future);
        Boolean result = future.get(5, TimeUnit.SECONDS);
        assertTrue(result, "异步发送应该成功");

        // 验证timestamp被自动设置
        assertNotNull(reportData.getTimestamp());
        assertTrue(reportData.getTimestamp() > 0);
    }

    @Test
    void testSendAsyncConcurrency() throws InterruptedException, ExecutionException, TimeoutException {
        if (reporterClient == null) {
            return;
        }

        // 测试并发发送
        int threadCount = 5;
        int messagesPerThread = 3;

        CompletableFuture<Void>[] futures = new CompletableFuture[threadCount];

        for (int i = 0; i < threadCount; i++) {
            final int threadIndex = i;
            futures[i] = CompletableFuture.runAsync(() -> {
                for (int j = 0; j < messagesPerThread; j++) {
                    ReportData reportData = ReporterClient.createManualReport(
                            "hdpt", "test", "并发集成测试", "concurrencyIntegrationTest",
                            "线程" + threadIndex + "消息" + j
                    );
                    reportData.setTraceId("integration_trace_" + threadIndex + "_" + j);

                    try {
                        CompletableFuture<Boolean> sendFuture = reporterClient.sendAsync(reportData);
                        Boolean result = sendFuture.get(5, TimeUnit.SECONDS);
                        assertTrue(result, "并发发送应该成功");
                    } catch (Exception e) {
                        fail("并发发送失败: " + e.getMessage());
                    }
                }
            });
        }

        // 等待所有线程完成
        CompletableFuture.allOf(futures).get(30, TimeUnit.SECONDS);
    }

    @Test
    void testSendAsyncKeyGeneration() throws ExecutionException, InterruptedException, TimeoutException {
        if (reporterClient == null) {
            return;
        }

        // 1. 测试使用traceId作为key
        ReportData reportDataWithTraceId = ReporterClient.createManualReport(
                "hdpt", "test", "Key生成集成测试1", "keyGenIntegrationTest1", "测试traceId作为key"
        );
        reportDataWithTraceId.setTraceId("integration_trace_123");
        reportDataWithTraceId.setBatchTraceId("integration_batch_456");

        CompletableFuture<Boolean> future1 = reporterClient.sendAsync(reportDataWithTraceId);
        assertTrue(future1.get(5, TimeUnit.SECONDS));

        // 2. 测试使用batchTraceId作为key（没有traceId时）
        ReportData reportDataWithBatchTraceId = ReporterClient.createManualReport(
                "hdpt", "test", "Key生成集成测试2", "keyGenIntegrationTest2", "测试batchTraceId作为key"
        );
        reportDataWithBatchTraceId.setBatchTraceId("integration_batch_789");
        // 不设置traceId

        CompletableFuture<Boolean> future2 = reporterClient.sendAsync(reportDataWithBatchTraceId);
        assertTrue(future2.get(5, TimeUnit.SECONDS));

        // 3. 测试使用timestamp作为key（没有traceId和batchTraceId时）
        ReportData reportDataWithTimestamp = ReporterClient.createManualReport(
                "hdpt", "test", "Key生成集成测试3", "keyGenIntegrationTest3", "测试timestamp作为key"
        );
        reportDataWithTimestamp.setTimestamp(1234567890L);
        // 不设置traceId和batchTraceId

        CompletableFuture<Boolean> future3 = reporterClient.sendAsync(reportDataWithTimestamp);
        assertTrue(future3.get(5, TimeUnit.SECONDS));
    }

    @Test
    void testSendAsyncWithEmptyStrings() throws ExecutionException, InterruptedException, TimeoutException {
        if (reporterClient == null) {
            return;
        }

        // 测试空字符串的处理
        ReportData reportData = ReporterClient.createManualReport(
                "hdpt", "test", "空字符串集成测试", "emptyStringIntegrationTest", "测试空字符串处理"
        );
        reportData.setTraceId(""); // 空字符串
        reportData.setBatchTraceId(""); // 空字符串
        reportData.setTimestamp(9876543210L);

        CompletableFuture<Boolean> future = reporterClient.sendAsync(reportData);
        assertTrue(future.get(5, TimeUnit.SECONDS));
    }
}
