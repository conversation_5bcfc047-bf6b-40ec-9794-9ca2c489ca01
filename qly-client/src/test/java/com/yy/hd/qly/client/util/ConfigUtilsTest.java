package com.yy.hd.qly.client.util;

import org.junit.jupiter.api.Test;

import java.util.Properties;

import static org.junit.jupiter.api.Assertions.*;

/**
 * ConfigUtils测试类
 * 
 * <AUTHOR>
 * @date 2025-07-31 17:10
 */
class ConfigUtilsTest {
    
    @Test
    void testLoadPropertiesFromClasspath() {
        // 测试加载存在的配置文件
        Properties properties = ConfigUtils.loadPropertiesFromClasspath("application.properties");
        assertNotNull(properties);
        assertTrue(properties.size() > 0);
        
        // 测试加载不存在的配置文件
        Properties emptyProperties = ConfigUtils.loadPropertiesFromClasspath("non-existent.properties");
        assertNotNull(emptyProperties);
        assertEquals(0, emptyProperties.size());
    }
    
    @Test
    void testGetProperty() {
        Properties properties = new Properties();
        properties.setProperty("test.key", "test.value");
        
        // 测试获取存在的配置
        String value = ConfigUtils.getProperty(properties, "test.key", "default");
        assertEquals("test.value", value);
        
        // 测试获取不存在的配置，返回默认值
        String defaultValue = ConfigUtils.getProperty(properties, "non.existent.key", "default");
        assertEquals("default", defaultValue);
    }
    
    @Test
    void testGetIntProperty() {
        Properties properties = new Properties();
        properties.setProperty("test.int", "123");
        properties.setProperty("test.invalid.int", "not-a-number");
        
        // 测试获取有效的整型配置
        int value = ConfigUtils.getIntProperty(properties, "test.int", 0);
        assertEquals(123, value);
        
        // 测试获取无效的整型配置，返回默认值
        int defaultValue = ConfigUtils.getIntProperty(properties, "test.invalid.int", 456);
        assertEquals(456, defaultValue);
        
        // 测试获取不存在的配置，返回默认值
        int nonExistentValue = ConfigUtils.getIntProperty(properties, "non.existent.key", 789);
        assertEquals(789, nonExistentValue);
    }
    
    @Test
    void testGetLongProperty() {
        Properties properties = new Properties();
        properties.setProperty("test.long", "123456789");
        properties.setProperty("test.invalid.long", "not-a-number");
        
        // 测试获取有效的长整型配置
        long value = ConfigUtils.getLongProperty(properties, "test.long", 0L);
        assertEquals(123456789L, value);
        
        // 测试获取无效的长整型配置，返回默认值
        long defaultValue = ConfigUtils.getLongProperty(properties, "test.invalid.long", 456L);
        assertEquals(456L, defaultValue);
        
        // 测试获取不存在的配置，返回默认值
        long nonExistentValue = ConfigUtils.getLongProperty(properties, "non.existent.key", 789L);
        assertEquals(789L, nonExistentValue);
    }
    
    @Test
    void testGetBooleanProperty() {
        Properties properties = new Properties();
        properties.setProperty("test.bool.true", "true");
        properties.setProperty("test.bool.false", "false");
        properties.setProperty("test.bool.invalid", "not-a-boolean");
        
        // 测试获取true值
        boolean trueValue = ConfigUtils.getBooleanProperty(properties, "test.bool.true", false);
        assertTrue(trueValue);
        
        // 测试获取false值
        boolean falseValue = ConfigUtils.getBooleanProperty(properties, "test.bool.false", true);
        assertFalse(falseValue);
        
        // 测试获取无效的布尔值，返回默认值
        boolean defaultValue = ConfigUtils.getBooleanProperty(properties, "test.bool.invalid", true);
        assertFalse(defaultValue); // Boolean.parseBoolean("not-a-boolean") 返回 false
        
        // 测试获取不存在的配置，返回默认值
        boolean nonExistentValue = ConfigUtils.getBooleanProperty(properties, "non.existent.key", true);
        assertTrue(nonExistentValue);
    }
    
    @Test
    void testPropertiesWithWhitespace() {
        Properties properties = new Properties();
        properties.setProperty("test.whitespace.string", "  value with spaces  ");
        properties.setProperty("test.whitespace.int", "  123  ");
        properties.setProperty("test.whitespace.long", "  456789  ");
        properties.setProperty("test.whitespace.bool", "  true  ");
        
        // 测试字符串值（不会自动trim）
        String stringValue = ConfigUtils.getProperty(properties, "test.whitespace.string", "default");
        assertEquals("  value with spaces  ", stringValue);
        
        // 测试整型值（会自动trim）
        int intValue = ConfigUtils.getIntProperty(properties, "test.whitespace.int", 0);
        assertEquals(123, intValue);
        
        // 测试长整型值（会自动trim）
        long longValue = ConfigUtils.getLongProperty(properties, "test.whitespace.long", 0L);
        assertEquals(456789L, longValue);
        
        // 测试布尔值（会自动trim）
        boolean boolValue = ConfigUtils.getBooleanProperty(properties, "test.whitespace.bool", false);
        assertTrue(boolValue);
    }
}
