<project>
   <modelVersion>4.0.0</modelVersion>
   <groupId>root.project</groupId>
   <artifactId>ejbs</artifactId>
   <packaging>ejb</packaging>
   <version>1.0</version>
   <name>enterprise java beans</name>
   <parent>
      <groupId>root</groupId>
      <artifactId>project</artifactId>
      <version>1.0</version>
   </parent>
   <dependencies>
      <dependency>
         <groupId>root.project</groupId>
         <artifactId>primary-source</artifactId>
      </dependency>
      <dependency>
         <groupId>root.project.projects</groupId>
         <artifactId>logging</artifactId>
      </dependency>
  </dependencies>
   <build>
      <plugins>
         <plugin>
            <artifactId>maven-ejb-plugin</artifactId>
            <configuration>
               <archive>
                  <manifest>
                     <addClasspath>true</addClasspath>
                  </manifest>
               </archive> 
            </configuration>
         </plugin>
      </plugins>
   </build>
</project>
