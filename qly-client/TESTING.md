# QlyClient 测试指南

本文档介绍如何运行和理解qly-client的测试。

## 测试结构

### 单元测试

#### ReporterClientTest
位置：`src/test/java/com/yy/hd/qly/client/ReporterClientTest.java`

**测试内容：**
- `testReportDataBuilder()` - 测试ReportData构建器
- `testCreateAutoReport()` - 测试自动上报数据创建
- `testCreateManualReport()` - 测试手动上报数据创建
- `testSendAsyncReturnType()` - 测试sendAsync方法的返回类型
- `testTimestampAutoSetting()` - 测试时间戳自动设置
- `testKeyGenerationLogic()` - 测试Key生成逻辑
- `testEmptyStringHandling()` - 测试空字符串处理
- `testDataTypeSettings()` - 测试数据类型设置

**特点：**
- 不依赖Spring上下文
- 测试静态方法和数据模型
- 验证业务逻辑正确性
- 运行速度快

#### JsonUtilsTest
位置：`src/test/java/com/yy/hd/qly/client/util/JsonUtilsTest.java`

**测试内容：**
- JSON序列化和反序列化
- 格式化JSON输出
- 深拷贝功能
- JSON有效性验证
- 空值处理

#### ConfigUtilsTest
位置：`src/test/java/com/yy/hd/qly/client/util/ConfigUtilsTest.java`

**测试内容：**
- 配置文件加载
- 各种数据类型的配置读取
- 默认值处理
- 空白字符处理

### 集成测试

#### ReporterClientIntegrationTest
位置：`src/test/java/com/yy/hd/qly/client/ReporterClientIntegrationTest.java`

**测试内容：**
- `testSendAsyncIntegration()` - 测试sendAsync集成功能
- `testSendAsyncWithNullTimestamp()` - 测试时间戳自动设置集成
- `testSendAsyncConcurrency()` - 测试并发发送集成
- `testSendAsyncKeyGeneration()` - 测试Key生成集成
- `testSendAsyncWithEmptyStrings()` - 测试空字符串处理集成

**特点：**
- 需要真实的Kafka环境
- 测试完整的发送流程
- 验证异步行为
- 测试并发安全性

## 运行测试

### 运行所有单元测试

```bash
mvn test
```

### 运行特定测试类

```bash
# 运行ReporterClient测试
mvn test -Dtest=ReporterClientTest

# 运行JsonUtils测试
mvn test -Dtest=JsonUtilsTest

# 运行ConfigUtils测试
mvn test -Dtest=ConfigUtilsTest
```

### 运行集成测试

集成测试需要真实的Kafka环境，默认情况下会被跳过。

#### 启动Kafka环境

1. 启动Zookeeper：
```bash
bin/zookeeper-server-start.sh config/zookeeper.properties
```

2. 启动Kafka：
```bash
bin/kafka-server-start.sh config/server.properties
```

3. 创建测试Topic：
```bash
bin/kafka-topics.sh --create --topic qly_test_topic --bootstrap-server localhost:9092 --partitions 1 --replication-factor 1
```

#### 运行集成测试

```bash
mvn test -Dintegration.test=true -Dtest=ReporterClientIntegrationTest
```

## sendAsync方法测试详解

### 测试覆盖范围

1. **基本功能测试**
   - 验证方法存在和返回类型
   - 测试数据模型的正确性

2. **时间戳处理测试**
   - 验证null时间戳自动设置
   - 验证已设置时间戳保持不变

3. **Key生成逻辑测试**
   - traceId优先级最高
   - batchTraceId次之
   - timestamp作为最后选择

4. **边界情况测试**
   - 空字符串处理
   - null值处理
   - 并发安全性

5. **集成测试**
   - 真实Kafka环境下的发送
   - 异步回调验证
   - 并发发送测试

### 测试策略

由于sendAsync方法依赖Spring上下文和Kafka连接，我们采用了分层测试策略：

1. **单元测试层**：测试不依赖外部系统的逻辑
2. **集成测试层**：测试完整的发送流程

### Mock策略说明

在早期版本中，我们尝试使用Mockito来mock依赖，但遇到了以下问题：
- Gson是final类，无法被mock
- Spring依赖注入在单元测试中复杂

因此，我们采用了更实用的方法：
- 单元测试专注于业务逻辑验证
- 集成测试验证完整流程

## 测试结果解读

### 成功的测试输出

```
Tests run: 8, Failures: 0, Errors: 0, Skipped: 0
```

### 集成测试跳过

```
Tests run: 1, Failures: 0, Errors: 0, Skipped: 1
```

这是正常的，因为集成测试需要特定的系统属性才会运行。

## 最佳实践

1. **开发时**：主要运行单元测试，速度快，反馈及时
2. **提交前**：运行所有单元测试确保代码质量
3. **发布前**：运行集成测试验证完整功能
4. **CI/CD**：单元测试每次构建运行，集成测试在特定环境运行

## 故障排除

### 常见问题

1. **测试编译失败**
   - 检查Java版本（需要Java 8+）
   - 检查Maven版本

2. **集成测试失败**
   - 确认Kafka服务正在运行
   - 检查网络连接
   - 验证Topic是否存在

3. **依赖问题**
   - 运行 `mvn clean install` 重新安装依赖
   - 检查网络连接到Maven仓库

### 调试技巧

1. 增加日志级别：
```properties
logging.level.com.yy.hd.qly.client=DEBUG
```

2. 运行单个测试方法：
```bash
mvn test -Dtest=ReporterClientTest#testSendAsyncReturnType
```

3. 跳过测试：
```bash
mvn install -DskipTests
```
