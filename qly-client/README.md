# QlyClient - 数据上报SDK

QlyClient是一个用于数据上报的SDK，支持通过Kafka发送数据上报消息。

## 功能特性

- 支持同步和异步数据上报
- 基于Kafka的可靠消息传输
- 使用Gson进行JSON序列化，性能优异
- 灵活的配置选项
- 支持批量上报场景
- 提供便捷的构建器模式
- 自动配置，开箱即用
- 提供JSON工具类，方便数据处理

## 快速开始

### 1. 添加依赖

在你的项目中添加qly-client依赖：

```xml
<dependency>
    <groupId>com.yy.hd.qly</groupId>
    <artifactId>qly-client</artifactId>
    <version>0.0.1-SNAPSHOT</version>
</dependency>
```

### 2. 配置

在`application.properties`中添加配置：

```properties
# Reporter Kafka 配置
qly.reporter.kafka.enabled=true
qly.reporter.kafka.bootstrap-servers=localhost:9092
qly.reporter.kafka.topic=qly_report_topic

# Kafka生产者配置
qly.reporter.kafka.producer.retries=3
qly.reporter.kafka.producer.batch-size=16384
qly.reporter.kafka.producer.acks=1
```

### 3. 使用示例

#### 基本使用

```java
@Autowired
private ReporterClient reporterClient;

public void actReportData() {
    // 创建上报数据
    ReportData actReportData = ReportData.builder()
            .batchTraceId("batch_001")
            .traceId("trace_001")
            .product("hdpt")
            .service("ranking")
            .scene("榜单结算事件")
            .method("updateRanking")
            .content("榜单结算成功")
            .actId(12345L)
            .uid(1001L)
            .build();
    
    // 异步发送（推荐）
    reporterClient.send(actReportData);
    
    // 或者同步发送
    boolean success = reporterClient.sendSync(actReportData);
}
```

#### 快速创建方法

```java
// 创建切面自动上报数据
ReportData autoReport = ReporterClient.createAutoReport(
    "hdpt", "stream", "单次送礼累榜", "commonNoticeUnicast", "累榜成功"
);
reporterClient.send(autoReport);

// 创建人工打点上报数据
ReportData manualReport = ReporterClient.createManualReport(
    "zhuiwan", "xlogic", "黑名单校验", "checkBlacklist", "累榜黑名单校验不通过，不累榜"
);
reporterClient.send(manualReport);
```

#### 异步发送并处理结果

```java
CompletableFuture<Boolean> future = reporterClient.sendAsync(actReportData);
future.thenAccept(result -> {
    if (result) {
        log.info("发送成功");
    } else {
        log.error("发送失败");
    }
});
```

#### JSON工具类使用

```java
import com.yy.hd.qly.client.util.JsonUtils;

// 对象转JSON
String json = JsonUtils.toJson(actReportData);

// 对象转格式化JSON
String prettyJson = JsonUtils.toPrettyJson(actReportData);

// JSON转对象
ReportData data = JsonUtils.fromJson(json, ReportData.class);

// 深拷贝对象
ReportData copy = JsonUtils.deepCopy(actReportData, ReportData.class);

// 验证JSON有效性
boolean isValid = JsonUtils.isValidJson(json);
```

## 数据字段说明

| 字段名 | 类型 | 说明 |
|--------|------|------|
| batchTraceId | String | 批次traceId，用于串联活动全程结算事件 |
| traceId | String | otel的traceId，用于串联调用链路 |
| traceBeginTime | Long | 业务时间(毫秒) |
| product | String | 产品，如hdpt、zhuiwan |
| service | String | 服务进程，如stream、xlogic、ranking |
| scene | String | 场景，可以是事件名称、源头入口方法名称等 |
| method | String | 上报埋点方法 |
| dataType | Integer | 数据类型: 1=切面自动上报 2=人工打点上报 |
| actId | Long | 活动id |
| cmptId | Long | 组件id |
| cmptIndex | Integer | 组件索引 |
| rankId | Long | 关联的榜单id，如无，填入0 |
| phaseId | Long | 关联的阶段id，如无，填入0 |
| timeCode | Long | 关联的榜单时间key，如无，填入0 |
| uid | Long | 产生这条数据的uid,如无，填入0 |
| sid | Long | 产生这条数据的sid,如无，填入0 |
| ssid | Long | 产生这条数据的ssid,如无，填入0 |
| key1 | String | 预留查询条件 |
| key2 | String | 预留查询条件 |
| key3 | String | 预留查询条件 |
| content | String | 上报主要内容 |
| extData | String | 预留扩展字段 |
| timestamp | Long | 上报时间戳(毫秒) |

## 配置选项

| 配置项 | 默认值 | 说明 |
|--------|--------|------|
| qly.reporter.kafka.enabled | true | 是否启用Reporter |
| qly.reporter.kafka.bootstrap-servers | localhost:9092 | Kafka服务器地址 |
| qly.reporter.kafka.topic | qly_report_topic | 上报数据的Topic名称 |
| qly.reporter.kafka.producer.retries | 3 | 重试次数 |
| qly.reporter.kafka.producer.batch-size | 16384 | 批量发送大小 |
| qly.reporter.kafka.producer.linger-ms | 1 | 延迟发送时间(毫秒) |
| qly.reporter.kafka.producer.buffer-memory | 33554432 | 缓冲区大小 |
| qly.reporter.kafka.producer.acks | 1 | 确认模式 |

## 环境配置

项目支持多环境配置，可以通过以下方式指定环境：

### 开发环境
```bash
java -jar qly-client.jar --spring.profiles.active=dev
```

### 测试环境
```bash
java -jar qly-client.jar --spring.profiles.active=test
```

### 生产环境
```bash
java -jar qly-client.jar --spring.profiles.active=prod
```

### 自定义配置
也可以通过JVM参数或环境变量覆盖配置：

```bash
# 通过JVM参数
java -Dqly.reporter.kafka.bootstrap-servers=prod-kafka:9092 -jar qly-client.jar

# 通过环境变量
export QLA_REPORTER_KAFKA_BOOTSTRAP_SERVERS=prod-kafka:9092
java -jar qly-client.jar
```

## 注意事项

1. 确保Kafka服务器正常运行并且可以访问
2. 建议使用异步发送方式以提高性能
3. 在生产环境中，请根据实际情况调整Kafka配置参数
4. timestamp字段如果不设置，会自动使用当前时间戳
