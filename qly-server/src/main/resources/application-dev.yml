yy:
  kafka:
    demo:
      listener:
        type: BATCH
      consumer:
        group-id: qly-${yy.env}
      enable-consumer: true
      enable-producer: true
      bootstrap-servers: kafkafs0014-test001.yy.com:8102,kafkafs0014-test002.yy.com:8102,kafkafs0014-test003.yy.com:8102,kafkafs0014-test004.yy.com:8102,kafkafs0014-test005.yy.com:8102,kafkafs0014-test006.yy.com:8102,kafkafs0014-test007.yy.com:8102
spring:
  datasource:
    dynamic:
      primary: ds1
      datasource:
        ds1:
          url: *************************************************************************************************************************************
          username: zhuiya_fs_test@zhuiya_test
