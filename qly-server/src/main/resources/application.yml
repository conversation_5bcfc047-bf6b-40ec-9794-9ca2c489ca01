spring:
  profiles:
    active: default,${MY_ENV_NAME:dev},${MY_ENV_NAME}-${yy.idc}
  servlet:
    multipart:
      enabled: false
yy:
  app-name: ${MY_PROJECT_NAME:qly}
  env: ${MY_ENV_NAME:dev}
  login:
    login-fail-http-status: 403
    ticket:
      appid: 5060
      auth-key: f4c4699cfe86414395deca409171079f
      auth-user: 1325544243
      repeat-use-ticket: false
    cookie:
      app-id: 5722
      app-key: 5z0LGzbHgtRoNfoz5nRxa1ri8hXlnEUL
      remote-valid: false
    all-validate: false
apollo:
  meta: http://nbcfg-dev.yy.com
  cluster: dev
  bootstrap:
    enabled: true
    eagerLoad:
      enabled: true
    namespaces: application-default.yml,application-special.yml
app:
  id: todo ref:https://sy.sysop.yy.com/service/overview/3@zhuiya@zhuiya-demo/release/configmanage?curCfg=Dynamic
logging:
  config: classpath:log4j2-${spring.application.env:dev}.xml
