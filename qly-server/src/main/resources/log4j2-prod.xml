<?xml version="1.0" encoding="UTF-8"?>
<Configuration status="WARN" monitorInterval="60" packages="com.yy.zhuiya.log4j.appender">
    <Properties>
        <Property name="jsonBasePath">/data/weblog/java</Property>
        <Property name="basePath">/data/weblog/java</Property>
        <Property name="application">${env:MY_PROJECT_NAME:-zhuiya}</Property>
        <Property name="logFile">all.log</Property>
        <Property name="errorLogFile">error.log</Property>
        <Property name="fileName">${basePath}/${application}/${logFile}</Property>
        <Property name="errorFileName">${basePath}/${errorLogFile}</Property>
        <!--供阿里云和loki采集-->
        <Property name="jsonLogFile">json.log2</Property>
        <Property name="jsonFileName">${jsonBasePath}/${application}/${jsonLogFile}</Property>
        <Property name="pattern">[%-5p]%d{DEFAULT}, [uid=%X{uid}, uri=%X{uri}, traceId=%X{trace_id}], [%t]%-c{1}:%L, %m%n</Property>
    </Properties>
    <Appenders>
        <Console name="Console" target="SYSTEM_OUT">
            <ThresholdFilter level="ERROR" onMatch="ACCEPT"/>
            <PatternLayout pattern="${pattern}" />
        </Console>

        <RollingRandomAccessFile name="AllFile" immediateFlush="true"
                                 fileName="${fileName}"
                                 filePattern="${fileName}.%d{yyyy-MM-dd-HH}.%i.gz" ignoreExceptions="false" append="true">
            <ThresholdFilter level="INFO" onMatch="ACCEPT"/>
            <PatternLayout pattern="${pattern}" />
            <Policies>
                <!--                <TimeBasedTriggeringPolicy interval="4" modulate="true"/>-->
                <!--不要太大,避免系统文件滚动-->
                <SizeBasedTriggeringPolicy size="300MB"/>
            </Policies>
            <DefaultRolloverStrategy>
                <Delete basePath="${basePath}/" maxDepth="1" testMode="false">
                    <!-- 只删除文件前缀为zhuiya.log.的文件 -->
                    <IfFileName glob="${logFile}.*.*.gz" >
                        <IfAny>
                            <!-- 日志保存时间 -->
                            <IfLastModified age="1d" />
                            <IfAccumulatedFileSize exceeds="10 GB" />
                            <IfAccumulatedFileCount exceeds="15" />
                        </IfAny>
                    </IfFileName>
                </Delete>
            </DefaultRolloverStrategy>
        </RollingRandomAccessFile>

        <RollingRandomAccessFile name="ErrorFile" immediateFlush="true"
                                 fileName="${errorFileName}"
                                 filePattern="${errorFileName}.%d{yyyy-MM-dd-HH}.%i.gz" ignoreExceptions="false" append="true">
            <ThresholdFilter level="WARN" onMatch="ACCEPT"/>
            <PatternLayout pattern="${pattern}" />
            <Policies>
                <!--                <TimeBasedTriggeringPolicy interval="4" modulate="true"/>-->
                <!--不要太大,避免系统文件滚动-->
                <SizeBasedTriggeringPolicy size="300MB"/>
            </Policies>
            <DefaultRolloverStrategy>
                <Delete basePath="${basePath}/" maxDepth="1" testMode="false">
                    <!-- 只删除文件前缀为zhuiya.log.的文件 -->
                    <IfFileName glob="${errorLogFile}.*.*.gz" >
                        <IfAny>
                            <!-- 日志保存时间 -->
                            <IfLastModified age="1d" />
                            <IfAccumulatedFileSize exceeds="10 GB" />
                            <IfAccumulatedFileCount exceeds="15" />
                        </IfAny>
                    </IfFileName>
                </Delete>
            </DefaultRolloverStrategy>
        </RollingRandomAccessFile>


        <RollingRandomAccessFile name="JsonFile" immediateFlush="true"
                                 fileName="${jsonFileName}"
                                 filePattern="${jsonFileName}.%d{yyyy-MM-dd-HH}.%i.gz" ignoreExceptions="false" append="true">
            <ThresholdFilter level="INFO" onMatch="ACCEPT"/>
            <JsonLayout compact="true" eventEol="true" properties="true" complete="false" locationInfo="true" includeStacktrace="true"
                        stacktraceAsString="true" objectMessageAsJsonObject="false" includeTimeMillis="true">
                <KeyValuePair key="traceId" value="$${ctx:trace_id:-}"/>
                <KeyValuePair key="timestamp" value="$${date:yyyy-MM-dd'T'HH:mm:ss.SSSZZ}" />
            </JsonLayout>
            <Policies>
                <!--                <TimeBasedTriggeringPolicy interval="4" modulate="true"/>-->
                <!--不要太大,避免系统文件滚动-->
                <SizeBasedTriggeringPolicy size="300MB"/>
            </Policies>
            <DefaultRolloverStrategy>
                <Delete basePath="${jsonBasePath}/${application}/" maxDepth="1" testMode="false">
                    <!-- 只删除文件前缀为zhuiya.log.的文件 -->
                    <IfFileName glob="${jsonLogFile}.*.*.gz" >
                        <IfAny>
                            <!-- 日志保存时间 -->
                            <IfLastModified age="1d" />
                            <IfAccumulatedFileSize exceeds="2 GB" />
                            <IfAccumulatedFileCount exceeds="2" />
                        </IfAny>
                    </IfFileName>
                </Delete>
            </DefaultRolloverStrategy>
        </RollingRandomAccessFile>

        <Monitor name="Monitor" srvname="java.yymusic.${application}">
            <ThresholdFilter level="ERROR" onMatch="ACCEPT"/>
            <PatternLayout pattern="${pattern}" />
        </Monitor>

        <ZhuiyaKafka name="ZhuiyaKafka" topic="zhuiya_log_topic">
            <ThresholdFilter level="ERROR" onMatch="ACCEPT"/>
            <JsonLayout compact="true" eventEol="true" properties="true" complete="false" locationInfo="true" includeStacktrace="true"
                        stacktraceAsString="true" objectMessageAsJsonObject="false" includeTimeMillis="true">
                <!--给一个默认值 00000000000000000000000000000000-->
                <KeyValuePair key="traceId" value="$${ctx:trace_id:-}"/>
                <!--                <KeyValuePair key="timestamp" value="$${date:yyyy-MM-dd'T'HH:mm:ss.SSSZZ}" />-->
            </JsonLayout>
            <Property name="bootstrap.servers">kafkawx006-core001.yy.com:8112,kafkawx006-core002.yy.com:8112,kafkawx006-core003.yy.com:8112</Property>
        </ZhuiyaKafka>
    </Appenders>
    <Loggers>
        <logger name="com.yy.ent.clients.s2s" level="ERROR" includeLocation="true">
            <AppenderRef ref="Console"/>
        </logger>
        <Root level="INFO" includeLocation="true">
            <AppenderRef ref="AllFile"/>
            <AppenderRef ref="JsonFile"/>
            <AppenderRef ref="Monitor"/>
            <AppenderRef ref="Console"/>
            <AppenderRef ref="ZhuiyaKafka"/>
            <appender-ref ref="ErrorFile" />
        </Root>
    </Loggers>
</Configuration>