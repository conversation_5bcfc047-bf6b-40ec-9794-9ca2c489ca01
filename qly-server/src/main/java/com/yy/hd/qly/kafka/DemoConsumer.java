package com.yy.hd.qly.kafka;

import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;

@Component
public class DemoConsumer {
    static public final String TOPIC = "qly_report_topic";
    private static Logger logger = LoggerFactory.getLogger(DemoConsumer.class);
    @KafkaListener(topics = TOPIC,
            containerFactory = "demoContainerFactory")
    public void aa(List<ConsumerRecord<String, String>> records){
        //批量listener
        records.parallelStream()
                .map(ConsumerRecord::value)
                .filter(Objects::nonNull)
                .forEach(logger::info);
    }

}
