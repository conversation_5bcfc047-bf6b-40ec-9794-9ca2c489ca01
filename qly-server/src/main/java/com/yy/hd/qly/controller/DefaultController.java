package com.yy.hd.qly.controller;

import com.yy.boot.component.common.EnvEnum;
import com.yy.boot.component.common.YYApplicationContext;
import jakarta.servlet.http.HttpServletResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.apache.commons.lang3.ArrayUtils;
import org.springframework.context.ApplicationContext;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.mvc.condition.PathPatternsRequestCondition;
import org.springframework.web.servlet.mvc.method.RequestMappingInfo;
import org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping;

import java.io.IOException;
import java.io.PrintWriter;
import java.util.*;
/**
 * @tageeIgnore
 * <AUTHOR> gen
 */
@RestController
public class DefaultController {

    private final static Logger logger = LoggerFactory.getLogger(DefaultController.class);

    /**
     * ping 接口
     * @return 时间戳
     */
    @GetMapping("/ping")
    public long ping() {
        return System.currentTimeMillis();
    }

    @GetMapping("/favicon.ico")
    @ResponseBody
    public void returnNoFavicon() {
    }

    @GetMapping("/robots.txt")
    public void robots(HttpServletResponse response) throws IOException {
        String robots = """
            User-agent: *
            Disallow: /
            """;
        response.setContentType("text/plain");
        PrintWriter writer = response.getWriter();
        writer.write(robots);
        writer.close();
    }

     @GetMapping("/index")
     public String index() {
        if(YYApplicationContext.getCurrentEnv() == EnvEnum.PROD) {
            return "prod";
        }
        ApplicationContext app = YYApplicationContext.getApplicationContext();
        String[] beanDefinitionNames =  app.getBeanDefinitionNames();
        List<String> patternList = new ArrayList<>();
        for (String beanDefinitionName : beanDefinitionNames) {
            Object bean = app.getBean(beanDefinitionName);
            if (bean instanceof RequestMappingHandlerMapping mapping) {
                Map<RequestMappingInfo, HandlerMethod> map = mapping.getHandlerMethods();
                for (Map.Entry<RequestMappingInfo, HandlerMethod> entry : map.entrySet()) {
                    RequestMappingInfo info = entry.getKey();
                    PathPatternsRequestCondition patterns = info.getPathPatternsCondition();
                    if (patterns != null) {
                        patternList.addAll(patterns.getPatternValues());
                    }
                }
            }
        }
        Collections.sort(patternList);
        String htmlHeader = """
                <!DOCTYPE html> 
                <html lang="en"> 
                <head> 
                <meta charset="UTF-8"> 
                <meta name="viewport" content="width=device-width, initial-scale=1.0"> 
                <title>${title}</title> 
                <link rel="stylesheet" href="https://yystatictest.bs2cdn.yy.com/mapping.css">
                </head>
                <body>
                <div class="grid-container"> 
                <h1>mapping uri</h1> 
                <ul class="file-list">""";
        StringBuilder sb = new StringBuilder(htmlHeader.replace("${title}", YYApplicationContext.getAppName()));
        for (String pattern : patternList) {
            String[] ignores = {"/index","/error", "/favicon.ico"};
            if (ArrayUtils.contains(ignores, pattern)) {
                continue;
            }
            sb.append("<li><a href=\"").append(pattern).append("\">").append(pattern).append("</a></li>").append("\n");
        }
        return sb.append("</ul></div></body> </html>").toString();
    }

}
