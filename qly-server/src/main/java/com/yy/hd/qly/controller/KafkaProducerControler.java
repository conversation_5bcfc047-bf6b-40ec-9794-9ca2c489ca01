package com.yy.hd.qly.controller;


import com.yy.hd.qly.kafka.DemoConsumer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
public class KafkaProducerControler {
    @Autowired
    @Qualifier("demoKafkaTemplate")
    private KafkaTemplate<String, String> demoKafkaTemplate;

    @RequestMapping("/send")
    public Object send(String msg){
        demoKafkaTemplate.send(DemoConsumer.TOPIC, msg);
        return System.currentTimeMillis();
    }
}

