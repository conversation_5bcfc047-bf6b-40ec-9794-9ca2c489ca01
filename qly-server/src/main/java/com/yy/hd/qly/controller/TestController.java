package com.yy.hd.qly.controller;

import com.yy.boot.component.common.EnvEnum;
import com.yy.boot.component.spring.condition.ConditionalOnEnv;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 用于测试接口，仅在开发测试环境中生效
 *
 * <AUTHOR> gen
 */
@RestController
@RequestMapping("test")
@ConditionalOnEnv({EnvEnum.TEST, EnvEnum.DEV})
public class TestController {

    private final static Logger logger = LoggerFactory.getLogger(TestController.class);

    /**
     * ping 接口
     *
     * @return 时间戳
     */
    @GetMapping("/ping")
    public long ping() {
        return System.currentTimeMillis();
    }

}

