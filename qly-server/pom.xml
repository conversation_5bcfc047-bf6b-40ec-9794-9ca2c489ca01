<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>com.yy.hd.qly</groupId>
		<artifactId>qly</artifactId>
		<version>0.0.1-SNAPSHOT</version>
		<relativePath>../pom.xml</relativePath>
	</parent>
	<artifactId>qly-server</artifactId>
	<name>qly-server</name>
	<description>入口模块，引导工程启动以及基础配置</description>
	<properties>
		<main.application>com.yy.hd.qly.Application</main.application>
	</properties>
	<dependencies>
		<dependency>
			<!-- 对接apollo:https://ku.baidu-int.com/d/607de46555e44a -->
			<groupId>com.yy.boot.component</groupId>
			<artifactId>apollo-spring-boot-starter</artifactId>
		</dependency>
		<dependency>
			<!-- 对spring kafka 封装，支持多实例:https://ku.baidu-int.com/d/WvdZmIhGsk3Ssj -->
			<groupId>com.yy.boot.component</groupId>
			<artifactId>kafka-spring-boot-starter</artifactId>
		</dependency>
		<dependency>
			<!-- log4j2:https://ku.baidu-int.com/d/180b42ebd79f4a -->
			<groupId>com.yy.boot.component</groupId>
			<artifactId>log4j2-spring-boot-starter</artifactId>
		</dependency>
		<dependency>
			<!-- 登录，支持ticket，cookie。开发测试环境模拟登录:https://ku.baidu-int.com/d/ALpS2hB6kYii5Y -->
			<groupId>com.yy.boot.component</groupId>
			<artifactId>login-spring-boot-starter</artifactId>
		</dependency>
		<dependency>
			<!-- mybatisPlus:https://ku.baidu-int.com/d/25400fa68f3f44 -->
			<groupId>com.yy.boot.component</groupId>
			<artifactId>mybatisplus-spring-boot-starter</artifactId>
		</dependency>
		<dependency>
			<!-- 提供http接口能力，登录、跨域、接口日志、鹰眼自动上报等功能:https://ku.baidu-int.com/d/SblkIWMKp8afAt -->
			<groupId>com.yy.boot.component</groupId>
			<artifactId>web-spring-boot-starter</artifactId>
		</dependency>
		<dependency>
			<groupId>com.yy.hd.qly</groupId>
			<artifactId>qly-repository</artifactId>
			<version>0.0.1-SNAPSHOT</version>
		</dependency>

		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-test</artifactId>
			<scope>test</scope>
			<exclusions>
				<exclusion>
					<groupId>org.springframework.boot</groupId>
					<artifactId>spring-boot-starter-logging</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
	</dependencies>

	<build>
		<plugins>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-dependency-plugin</artifactId>
			</plugin>
		</plugins>
	</build>

</project>
